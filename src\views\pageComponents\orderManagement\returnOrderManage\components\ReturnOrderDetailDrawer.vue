<template>
  <a-drawer width="45vw" title="退换单详情" :open="visible" @close="handleClose" :maskClosable="false" :bodyStyle="{ padding: '0' }">
    <template #extra>
      <a-space>
        <a-button @click="handlePrevious" :disabled="!canGoPrevious">上一条</a-button>
        <a-button @click="handleNext" :disabled="!canGoNext">下一条</a-button>
      </a-space>
    </template>

    <div class="detail-form-container">
      <LoadingOutlined v-show="loading" class="loadingIcon" />

      <div v-if="!loading && detailData">
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="drawer-title">退换单基本信息</div>

          <!-- 第一行四列布局 -->
          <div class="info-row-grid">
            <div class="info-item">
              <div class="info-label">退货申请单编号</div>
              <div class="info-content">
                {{ detailData.number || '--' }}
                <CopyOutlined v-if="detailData.number" class="copy-icon" @click="copyToClipboard(detailData.number, '退货申请单编号')" />
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">关联采购订单编号</div>
              <div class="info-content">
                <div v-if="detailData.purchase_order_numbers && detailData.purchase_order_numbers.length > 0">
                  <div v-for="(orderNum, index) in detailData.purchase_order_numbers" :key="index" class="order-number-item">
                    {{ orderNum }}
                    <CopyOutlined class="copy-icon" @click="copyToClipboard(orderNum, '采购订单编号')" />
                  </div>
                </div>
                <span v-else>--</span>
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">仓库</div>
              <div class="info-content">{{ detailData.warehouse_name || '--' }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">供应商</div>
              <div class="info-content">{{ detailData.company_supplier_name || '--' }}</div>
            </div>
          </div>

          <!-- 第二行四列布局 -->
          <div class="info-row-grid">
            <div class="info-item">
              <div class="info-label">申请类型</div>
              <div class="info-content">{{ detailData.application_type_string || '--' }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">退货原因</div>
              <div class="info-content">{{ detailData.return_reason_type_string || '--' }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">申请人</div>
              <div class="info-content">{{ detailData.creator_name || '--' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">申请时间</div>
              <div class="info-content">{{ detailData.create_at_string || '--' }}</div>
            </div>
          </div>
        </div>

        <!-- 收货信息 -->
        <div class="form-section">
          <div class="drawer-title">收货信息</div>

          <!-- 一行四列布局 -->
          <div class="info-row-grid">
            <div class="info-item">
              <div class="info-label">收货人</div>
              <div class="info-content">{{ detailData.consignee || '--' }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">联系电话</div>
              <div class="info-content">{{ detailData.phone_number || '--' }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">收货地址</div>
              <div class="info-content">
                {{ [detailData.province, detailData.city, detailData.area, detailData.shipments_address].filter(Boolean).join(' ') || '--' }}
              </div>
            </div>

            <div class="info-item">
              <!-- 空占位，保持四列布局 -->
              <div class="info-label">&nbsp;</div>
              <div class="info-content">&nbsp;</div>
            </div>
          </div>
        </div>

        <!-- 退换明细 -->
        <div class="form-section">
          <div class="drawer-title">退换明细</div>

          <div class="table-container">
            <a-table
              :columns="productColumns"
              :data-source="detailData.purchaseReturnApplicationDetails || []"
              :pagination="false"
              size="small"
              bordered
              :scroll="{ x: 1200 }"
              class="!text-12px overflow-x-auto"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'sku_name'">
                  <div class="flex items-start text-left">
                    <div class="main_image">
                      <!-- 使用a-image组件实现图片预览功能 -->
                      <a-image
                        v-if="record.image_url && record.image_url.trim()"
                        :src="record.image_url"
                        class="product-image clickable-image"
                        :preview="{
                          onVisibleChange: (visible) => handlePreviewVisibleChange(record, visible),
                          src: record.image_url,
                        }"
                        @error="handleImageError(record)"
                        @load="handleImageLoad(record)"
                      >
                        <template #previewMask>
                          <EyeOutlined />
                        </template>
                      </a-image>

                      <!-- 加载状态 -->
                      <div v-else-if="isImageLoading(record)" class="image-loading">
                        <LoadingOutlined />
                      </div>

                      <!-- 无图片时显示 -->
                      <a-image v-else :src="defaultImageUrl" class="product-image" :preview="false" />
                    </div>
                    <div class="flex flex-col ml-4">
                      <div class="lh-20">{{ record.sku_name }}</div>
                      <div class="lh-20">
                        <span class="c-#999">类目：</span>
                        <span>{{ record.all_category || '--' }}</span>
                      </div>
                      <div class="lh-20">
                        <span class="c-#999">规格：</span>
                        <span>{{ record.type_specification || '--' }}</span>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'srs_supplier_prod_code'">
                  <div class="text-left">
                    <div>
                      <span class="c-#999">供应商：</span>
                      <span>{{ record.srs_supplier_prod_code || '--' }}</span>
                    </div>
                    <div>
                      <span class="c-#999">平台：</span>
                      <span>{{ record.srs_platform_prod_code || '--' }}</span>
                    </div>
                  </div>
                </template>
                <template v-else-if="column.key === 'tax_unit_price'">¥{{ (record.tax_unit_price || 0).toFixed(2) }}</template>
                <template v-else-if="column.key === 'sum_price'">¥{{ (record.sum_price || 0).toFixed(2) }}</template>
                <template v-else-if="column.key === 'return_amount'">¥{{ (record.return_amount || 0).toFixed(2) }}</template>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 费用汇总 -->
        <div class="form-section">
          <div class="summary-container">
            <div class="summary-item">
              <span class="summary-label">商品退库总价：</span>
              <span class="summary-value">¥{{ (detailData.total_product_return_amount || 0).toFixed(2) }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">
                其他费用
                <a-tooltip v-if="detailData.other_fee_remark" :title="detailData.other_fee_remark">
                  <QuestionCircleOutlined class="info-icon" />
                </a-tooltip>
                ：
              </span>
              <span class="summary-value">¥{{ (detailData.other_fee_amount || 0).toFixed(2) }}</span>
            </div>
            <div class="summary-total">
              <span class="total-label">退库总金额总计：</span>
              <span class="total-value">¥{{ (detailData.total_return_amount || 0).toFixed(2) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { QuestionCircleOutlined, CopyOutlined, LoadingOutlined, EyeOutlined } from '@ant-design/icons-vue'
import { GetReturnOrderDetail } from '@/servers/returnOrderManage'
import { message } from 'ant-design-vue'
import { ref, computed, watch } from 'vue'
// 导入本地默认图片
import defaultImageUrl from '@/assets/image/wutu.png'

interface ReturnOrderDetail {
  id: number
  number: string
  purchase_order_numbers: string[]
  warehouse_id: number
  warehouse_name: string
  company_supplier_name: string
  company_supplier_id: number
  application_type: string
  application_type_string: string
  return_reason_type: string
  return_reason_type_string: string
  create_at: string
  create_at_string: string
  creator_id: number
  creator_name: string
  consignee: string
  phone_number: string
  province: string
  city: string
  area: string
  shipments_address: string
  purchaseReturnApplicationDetails: any[]
  total_product_return_amount: number
  other_fee_amount: number
  other_fee_remark: string
  total_return_amount: number
}

const visible = ref(false)
const loading = ref(false)
const detailData = ref<ReturnOrderDetail | null>(null)
const currentIndex = ref(-1)
const tableData = ref<any[]>([])

// 图片状态管理
const imageStates = ref<{
  [key: number]: {
    loading: boolean
    error: boolean
  }
}>({})

// 商品明细表格列配置
const productColumns = [
  {
    title: '序号',
    key: 'index',
    width: 100,
    align: 'center' as const,
    customRender: ({ index }: { index: number }) => index + 1,
  },
  {
    title: '关联采购订单编号',
    dataIndex: 'number',
    key: 'number',
    width: 250,
  },
  {
    title: '商品',
    dataIndex: 'sku_name',
    key: 'sku_name',
    width: 500,
  },
  {
    title: '商品编码',
    dataIndex: 'srs_supplier_prod_code',
    key: 'srs_supplier_prod_code',
    width: 250,
  },
  {
    title: '采购数量',
    dataIndex: 'purchase_quantity',
    key: 'purchase_quantity',
    width: 250,
    align: 'left' as const,
  },
  {
    title: '采购含税单价',
    dataIndex: 'tax_unit_price',
    key: 'tax_unit_price',
    width: 200,
    align: 'right' as const,
  },
  {
    title: '采购总金额',
    dataIndex: 'sum_price',
    key: 'sum_price',
    width: 200,
    align: 'right' as const,
  },
  {
    title: '退库数量',
    dataIndex: 'return_quantity',
    key: 'return_quantity',
    width: 250,
    align: 'left' as const,
  },
  {
    title: '退库金额',
    dataIndex: 'return_amount',
    key: 'return_amount',
    width: 120,
    align: 'right' as const,
  },
  {
    title: '实际退库数量',
    dataIndex: 'actual_return_quantity',
    key: 'actual_return_quantity',
    width: 350,
    align: 'left' as const,
  },
]

// 计算是否可以上一条/下一条
const canGoPrevious = computed(() => currentIndex.value > 0)
const canGoNext = computed(() => currentIndex.value < tableData.value.length - 1)

// 获取详情数据 - 使用模拟数据
const fetchDetail = async (id: number) => {
  try {
    loading.value = true

    const res = await GetReturnOrderDetail({ id })

    detailData.value = res.data

    // 模拟API延迟
    // await new Promise((resolve) => setTimeout(resolve, 500))

    // 模拟数据 - 使用新的图片地址
    // const mockData = {
    //   id,
    //   number: `TH${String(id).padStart(6, '0')}`,
    //   purchase_order_numbers: [`PO${String(id).padStart(6, '0')}001`, `PO${String(id).padStart(6, '0')}002`],
    //   warehouse_id: 1,
    //   warehouse_name: '广州仓库',
    //   company_supplier_name: '深圳市优质供应商有限公司',
    //   company_supplier_id: companySupplierID,
    //   application_type: '10',
    //   application_type_string: '仅退款',
    //   return_reason_type: '10',
    //   return_reason_type_string: '采购退货',
    //   create_at: '2025-08-11T07:29:40.540Z',
    //   create_at_string: '2025-08-11 15:29:40',
    //   creator_id: 1001,
    //   creator_name: '张三',
    //   consignee: '李四',
    //   phone_number: '13888888888',
    //   province: '广东省',
    //   city: '广州市',
    //   area: '天河区',
    //   shipments_address: '天河路123号科技大厦A座1001室',
    //   purchaseReturnApplicationDetails: [
    //     {
    //       id: 1,
    //       number: `DTL${String(id).padStart(6, '0')}001`,
    //       image_url: 'https://resource.westmonth.com/6647647d2e785f/2085a369ab5f4aaca97c3377165b059d_1.png',
    //       sku_name: '苹果iPhone 15 Pro Max 256GB 深空黑色',
    //       all_category: '数码产品/手机/苹果',
    //       srs_supplier_prod_code: 'SUP001234567',
    //       srs_platform_prod_code: 'PLT987654321',
    //       type_specification: '256GB/深空黑色',
    //       purchase_quantity: 10,
    //       tax_unit_price: 8999.0,
    //       sum_price: 89990.0,
    //       return_quantity: 2,
    //       return_amount: 17998.0,
    //       actual_return_quantity: 2,
    //     },
    //     {
    //       id: 2,
    //       number: `DTL${String(id).padStart(6, '0')}002`,
    //       image_url: 'https://resource.westmonth.com/6647647d2e785f/1065c32a821044959b94687a642e4a19_wechat_2025-07-04_152415_268.png',
    //       sku_name: '华为Mate 60 Pro 512GB 雅川青',
    //       all_category: '数码产品/手机/华为',
    //       srs_supplier_prod_code: 'SUP001234568',
    //       srs_platform_prod_code: 'PLT987654322',
    //       type_specification: '512GB/雅川青',
    //       purchase_quantity: 5,
    //       tax_unit_price: 6999.0,
    //       sum_price: 34995.0,
    //       return_quantity: 1,
    //       return_amount: 6999.0,
    //       actual_return_quantity: 1,
    //     },
    //     {
    //       id: 3,
    //       number: `DTL${String(id).padStart(6, '0')}003`,
    //       image_url: 'https://resource.westmonth.com/6647647d2e785f/1065c32a821044959b94687a642e4a19_wechat_2025-07-04_152415_268.png',
    //       sku_name: '小米14 Ultra 16GB+1TB 钛金属黑',
    //       all_category: '数码产品/手机/小米',
    //       srs_supplier_prod_code: 'SUP001234569',
    //       srs_platform_prod_code: 'PLT987654323',
    //       type_specification: '16GB+1TB/钛金属黑',
    //       purchase_quantity: 3,
    //       tax_unit_price: 6499.0,
    //       sum_price: 19497.0,
    //       return_quantity: 1,
    //       return_amount: 6499.0,
    //       actual_return_quantity: 0,
    //     },
    //   ],
    //   total_product_return_amount: 31496.0,
    //   other_fee_amount: 200.0,
    //   other_fee_remark: '包含运费150元，包装费50元',
    //   total_return_amount: 31696.0,
    // }

    // detailData.value = mockData

    // 初始化图片状态
    // if (mockData.purchaseReturnApplicationDetails) {
    //   mockData.purchaseReturnApplicationDetails.forEach((detail: any) => {
    //     const hasImage = detail.image_url && detail.image_url.trim()
    //     imageStates.value[detail.id] = {
    //       loading: !!hasImage,
    //       error: false,
    //     }
    //   })
    // }
  } catch (error) {
    console.error('获取退库单详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 上一条
const handlePrevious = () => {
  if (canGoPrevious.value) {
    currentIndex.value--
    const prevItem = tableData.value[currentIndex.value]
    fetchDetail(prevItem.pId || prevItem.id)
  } else {
    message.info('已经是第一条')
  }
}

// 下一条
const handleNext = () => {
  if (canGoNext.value) {
    currentIndex.value++
    const nextItem = tableData.value[currentIndex.value]
    // console.log('吓一跳tableData', tableData.value)
    // console.log('吓一跳nextItem', nextItem)
    fetchDetail(nextItem.pId || nextItem.id)
  } else {
    message.info('已经是最后一条')
  }
}

// 复制到剪贴板
// 复制到剪贴板（兼容处理）
const copyToClipboard = async (text: string, label: string) => {
  try {
    // 现代浏览器支持 clipboard API
    if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
      await navigator.clipboard.writeText(text)
      message.success(`${label}已复制到剪贴板`)
      return
    }

    // 降级方案：创建临时文本框执行复制
    const textarea = document.createElement('textarea')
    textarea.value = text
    // 隐藏文本框（避免影响页面布局）
    textarea.style.position = 'fixed'
    textarea.style.top = '-999px'
    textarea.style.left = '-999px'
    document.body.appendChild(textarea)
    // 选中并复制
    textarea.select()
    document.execCommand('copy')
    // 清理临时元素
    document.body.removeChild(textarea)
    message.success(`${label}已复制到剪贴板`)
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  }
}

// 关闭抽屉
const handleClose = () => {
  visible.value = false
  detailData.value = null
  currentIndex.value = -1
  tableData.value = []
  // 清空图片状态
  imageStates.value = {}
}

// 打开抽屉
const open = (row: any, data: any[], index: number) => {
  console.log('open打开冲突', row)

  visible.value = true
  tableData.value = data
  currentIndex.value = index
  fetchDetail(row.pId || row.id)
}

// 图片预览状态变化
const handlePreviewVisibleChange = (_record: any, visible: boolean) => {
  // 可以在这里添加预览状态变化时的逻辑
  if (visible) {
    // 预览打开时的处理
  } else {
    // 预览关闭时的处理
  }
}

// 处理图片加载完成
const handleImageLoad = (record: any) => {
  if (imageStates.value[record.id]) {
    imageStates.value[record.id].loading = false
    imageStates.value[record.id].error = false
  }
}

// 处理图片加载错误
const handleImageError = (record: any) => {
  console.error(`商品图片加载失败: ${record.id}`)
  if (imageStates.value[record.id]) {
    imageStates.value[record.id].loading = false
    imageStates.value[record.id].error = true
  }
}

// 检查图片是否处于加载状态
const isImageLoading = (record: any) => {
  const hasImage = record.image_url && record.image_url.trim()
  return !!hasImage && imageStates.value[record.id]?.loading === true
}

// 监听详情数据变化，更新图片状态
watch(
  () => detailData.value?.purchaseReturnApplicationDetails,
  (newDetails) => {
    if (newDetails) {
      newDetails.forEach((detail: any) => {
        const hasImage = detail.image_url && detail.image_url.trim()
        if (!imageStates.value[detail.id]) {
          imageStates.value[detail.id] = {
            loading: !!hasImage,
            error: false,
          }
        } else {
          imageStates.value[detail.id].loading = !!hasImage
        }
      })
    }
  },
  { deep: true },
)

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.detail-form-container {
  padding: 24px;
  padding-left: 30px;
}

.form-section {
  margin-bottom: 32px;
}

.drawer-title {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  padding-left: 12px;
  /* 14【粗体】 */

  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  /* 文字/333 */
  color: #333;
  letter-spacing: 0;
}

.drawer-title::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 2px;
  height: 14px;
  content: '';
  background: #1890ff;
  transform: translateY(-50%);
}

// 四列网格布局
.info-row-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  z-index: 0;
  margin-bottom: 4px;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  color: #999;
  letter-spacing: 0;
}

.info-content {
  position: relative;
  z-index: 0;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  color: #333;
  letter-spacing: 0;
  word-break: break-word;
}

.copy-icon {
  margin-left: 8px;
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;

  &:hover {
    color: #40a9ff;
  }
}

.order-number-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.table-container {
  margin-top: 16px;
}

.main_image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.product-image {
  display: block;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: all 0.3s ease;
}

.clickable-image {
  cursor: pointer;
}

.clickable-image:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  transform: scale(1.05);
}

.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  font-size: 12px;
  color: #999;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}

.image-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  color: #1890ff;
}

// 图片预览遮罩样式
:deep(.ant-image-preview-mask) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.summary-container {
  z-index: 3;
  display: flex;
  flex-direction: column;

  // gap: 12px;
  align-items: flex-end;
  padding: 20px 0 0;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;

  .summary-label {
    z-index: 0;
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    color: #999;
    letter-spacing: 0;
  }

  .summary-value {
    z-index: 1;
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    color: #999;
    letter-spacing: 0;
  }
}

.summary-total {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 16px;
  font-weight: 600;

  .total-label {
    z-index: 0;
    font-size: 12px;
    font-weight: 600;
    line-height: 20px;
    color: #333;
    letter-spacing: 0;
  }

  .total-value {
    z-index: 1;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: #333;
    text-align: right;
    letter-spacing: 0;
  }
}

.info-icon {
  margin-left: 4px;
  color: #1890ff;
  cursor: help;
}

.loadingIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 24px;
  color: #1890ff;
  transform: translate(-50%, -50%);
}

:deep(.ant-table-tbody > tr > td) {
  z-index: 0;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  color: #333;
  text-align: center;
  letter-spacing: 0;
}

:deep(.ant-table-thead > tr > th) {
  z-index: 1;
  font-size: 12px;
  font-weight: 600;
  line-height: 20px;
  color: #333;
  text-align: center;
  letter-spacing: 0;
  background: #f8f8f9;
}

// 表格行 hover 样式
:deep(.ant-table-tbody > tr:hover) {
  cursor: pointer;
  background-color: #f5f7fa;
}

// 与发货单一致的表格样式
:deep(.ant-table-cell) {
  white-space: nowrap;
}
</style>
